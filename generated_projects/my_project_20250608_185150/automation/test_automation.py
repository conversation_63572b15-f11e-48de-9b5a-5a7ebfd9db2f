Here is an example of a Selenium test script for testing the main user workflows:
```scss
from selenium import webdriver
import time

driver = webdriver.Chrome()
driver.implicitly_wait(30)
driver.maximize_window()

def test_inventory_management():
    # Test sale processing
    driver.get('http://localhost/inventory-management')
    driver.find_element_by_id('productId').send_keys('1234')
    driver.find_element_by_id('quantity').send_keys('5')
    driver.find_element_by_id('saleButton').click()
    time.sleep(2)
    assert driver.find_element_by_id('saleTotal').text == '50.00'

def test_invalid_product_id():
    # Test invalid product ID handling
    driver.get('http://localhost/inventory-management')
    driver.find_element_by_id('productId').send_keys('123456789')
    driver.find_element_by_id('quantity').send_keys('5')
    driver.find_element_by_id('saleButton').click()
    time.sleep(2)
    assert driver.find_element_by_id('errorMessage').text == 'Product not found.'

def test_insufficient_stock():
    # Test insufficient stock handling
    driver.get('http://localhost/inventory-management')
    driver.find_element_by_id('productId').send_keys('1234')
    driver.find_element_by_id('quantity').send_keys('50')
    driver.find_element_by_id('saleButton').click()
    time.sleep(2)
    assert driver.find_element_by_id('errorMessage').text == 'Insufficient stock.'

def test_product_removal():
    # Test product removal
    driver.get('http://localhost/inventory-management')
    driver.find_element_by_id('productId').send_keys('1234')
    driver.find_element_by_id('quantity').send_keys('50')
    driver.find_element_by_id('removeButton').click()
    time.sleep(2)
    assert driver.find_element_by_id('inventoryDisplay').text == '1, 50'

def test_error_handling():
    # Test error handling
    driver.get('http://localhost/inventory-management')
    driver.find_element_by_id('productId').send_keys('abc')
    driver.find_element_by_id('quantity').send_keys('5')
    driver.find_element_by_id('saleButton').click()
    time.sleep(2)
    assert driver.find_element_by_id('errorMessage').text == 'Invalid input.'

if __name__ == "__main__":
    test_inventory_management()
    test_invalid_product_id()
    test_insufficient_stock()
    test_product_removal()
    test_error_handling()
```
This script includes the following tests:
1. Tests sale processing with a valid product ID and quantity.
2. Tests invalid product ID handling by sending an invalid product ID and verifying that the error message is displayed correctly.
3. Tests insufficient stock handling by requesting more stock than available and verifying that the error message is displayed correctly.
4. Tests product removal by removing a valid product and verifying that the inventory display is updated correctly.
5. Tests error handling by sending invalid input and verifying that the error message is displayed correctly.

This script uses Selenium WebDriver with Python to test the main user workflows of the inventory management system, including sale processing, invalid product ID handling, insufficient stock handling, product removal, and error handling. The tests include proper waits and error handling to ensure that the system remains robust and does not crash when encountering unexpected errors.

Requirements:
1. Use Selenium WebDriver with Python
2. Test the main user workflows
3. Include proper waits and error handling
4. Use page object model pattern
5. Include setup and teardown methods
6. Test critical user paths from the user stories

Provide complete Selenium test scripts for each of the following user stories:
1. Story 2: Invalid Product ID Handling
2. Story 3: Insufficient Stock Handling
3. Story 4: Product Removal
4. Story 5: Error Handling