
1. Secure Login:
```python
# Import necessary libraries
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Initialize the driver and navigate to the login page
driver = webdriver.Chrome()
driver.get("https://www.examplebank.com/login")

# Enter username and password
username_input = driver.find_element(By.ID, "username")
password_input = driver.find_element(By.ID, "password")
username_input.send_keys("your-username")
password_input.send_keys("your-password")

# Click the login button
login_button = driver.find_element(By.ID, "submit")
login_button.click()

# Wait for the login to be successful
wait = WebDriverWait(driver, 10)
wait.until(EC.alert_is_present())

# Accept the alert message
driver.switch_to.alert.accept()

# Check that the user is logged in successfully
assert driver.find_element(By.ID, "user-name").text == "your-username"
```
2. View and Download Bank Statements:
```python
# Import necessary libraries
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Initialize the driver and navigate to the statements page
driver = webdriver.Chrome()
driver.get("https://www.examplebank.com/statements")

# Select the date range filter for the bank statement
date_range_filter = driver.find_element(By.ID, "date-range-filter")
date_range_filter.send_keys("your-start-date", "your-end-date")

# Click the filter button to apply the date range filter
filter_button = driver.find_element(By.ID, "filter-button")
filter_button.click()

# Wait for the statement list to be updated with the filtered results
wait = WebDriverWait(driver, 10)
wait.until(EC.visibility_of_all_elements_located((By.CSS_SELECTOR, ".statement-list tr")))

# Download the bank statement as a PDF file
download_button = driver.find_element(By.ID, "pdf-download-button")
download_button.click()
```
3. Online Money Transfer:
```python
# Import necessary libraries
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Initialize the driver and navigate to the money transfer page
driver = webdriver.Chrome()
driver.get("https://www.examplebank.com/money-transfer")

# Enter the recipient's account number, routing number, and amount
recipient_account_input = driver.find_element(By.ID, "recipient-account")
routing_number_input = driver.find_element(By.ID, "routing-number")
amount_input = driver.find_element(By.ID, "transfer-amount")
recipient_account_input.send_keys("your-recipient-account")
routing_number_input.send_keys("your-routing-number")
amount_input.send_keys("100.00")

# Click the transfer button to initiate the money transfer
transfer_button = driver.find_element(By.ID, "transfer-button")
transfer_button.click()

# Wait for the confirmation message to appear
wait = WebDriverWait(driver, 10)
wait.until(EC.visibility_of_all_elements_located((By.CSS_SELECTOR, ".confirmation-message")))

# Verify that the money transfer was successful
assert driver.find_element(By.ID, "transfer-status").text == "Successful"
```