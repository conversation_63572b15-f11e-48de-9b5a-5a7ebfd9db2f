import streamlit as st
import os
import tempfile
import subprocess
import strands
import ollama

# Configure the page
st.set_page_config(page_title="SDLC Pipeline", layout="wide")
st.title("SDLC Pipeline with AI Agents")

# Define our agents
class RequirementsAnalyzer(strands.Agent):
    def run(self, requirements_doc):
        st.write("📝 Analyzing requirements document...")
        response = ollama.chat(model="llama3", messages=[
            {"role": "system", "content": "You are a requirements analyst. Extract key requirements and context."},
            {"role": "user", "content": f"Analyze this requirements document and provide a structured summary:\n\n{requirements_doc}"}
        ])
        return response['message']['content']

class UserStoryGenerator(strands.Agent):
    def run(self, requirements_analysis):
        st.write("📚 Generating user stories...")
        response = ollama.chat(model="llama3", messages=[
            {"role": "system", "content": "You are a product owner. Create user stories from requirements."},
            {"role": "user", "content": f"Create user stories from these requirements:\n\n{requirements_analysis}"}
        ])
        return response['message']['content']

class CodeGenerator(strands.Agent):
    def run(self, user_stories):
        st.write("💻 Generating code...")
        response = ollama.chat(model="codellama", messages=[
            {"role": "system", "content": "You are a software developer. Generate code based on user stories."},
            {"role": "user", "content": f"Generate a complete application based on these user stories:\n\n{user_stories}"}
        ])
        return response['message']['content']

class TestCaseGenerator(strands.Agent):
    def run(self, code, user_stories):
        st.write("🧪 Generating unit tests...")
        response = ollama.chat(model="codellama", messages=[
            {"role": "system", "content": "You are a QA engineer. Create unit tests for code."},
            {"role": "user", "content": f"Create unit tests for this code based on these user stories:\n\nUser Stories:\n{user_stories}\n\nCode:\n{code}"}
        ])
        return response['message']['content']

class AutomationScriptGenerator(strands.Agent):
    def run(self, code, user_stories):
        st.write("🤖 Generating Selenium automation scripts...")
        response = ollama.chat(model="codellama", messages=[
            {"role": "system", "content": "You are a QA automation engineer. Create Selenium scripts."},
            {"role": "user", "content": f"Create Selenium automation scripts for this application based on these user stories:\n\nUser Stories:\n{user_stories}\n\nCode:\n{code}"}
        ])
        return response['message']['content']

class DeploymentScriptGenerator(strands.Agent):
    def run(self, code):
        st.write("🚀 Generating deployment scripts...")
        response = ollama.chat(model="codellama", messages=[
            {"role": "system", "content": "You are a DevOps engineer. Create deployment scripts."},
            {"role": "user", "content": f"Create deployment scripts (Docker, etc.) for this application:\n\n{code}"}
        ])
        return response['message']['content']

class CodeExecutor(strands.Agent):
    def run(self, code, deployment_script):
        st.write("▶️ Preparing to execute code...")
        
        # Create a temporary directory for the project
        with tempfile.TemporaryDirectory() as temp_dir:
            # Write the code to a file
            app_path = os.path.join(temp_dir, "app.py")
            with open(app_path, "w") as f:
                f.write(code)
            
            # Write the deployment script
            deploy_path = os.path.join(temp_dir, "deploy.sh")
            with open(deploy_path, "w") as f:
                f.write(deployment_script)
            os.chmod(deploy_path, 0o755)
            
            # Execute the code (simplified for demo)
            st.write(f"📂 Project files created in temporary directory: {temp_dir}")
            st.write("⚙️ Executing code (simulated)...")
            
            # In a real scenario, you would execute the code here
            # For safety, we're just listing the files
            result = subprocess.run(["ls", "-la", temp_dir], capture_output=True, text=True)
            return f"Execution completed. Directory contents:\n{result.stdout}"

# Create the SDLC pipeline strand
def create_sdlc_pipeline():
    requirements_analyzer = RequirementsAnalyzer()
    user_story_generator = UserStoryGenerator()
    code_generator = CodeGenerator()
    test_case_generator = TestCaseGenerator()
    automation_script_generator = AutomationScriptGenerator()
    deployment_script_generator = DeploymentScriptGenerator()
    code_executor = CodeExecutor()
    
    # Define the workflow
    strand = strands.Workflow()  # Changed from strands.Strand() to strands.Workflow()
    strand.add_node(requirements_analyzer)
    strand.add_node(user_story_generator)
    strand.add_node(code_generator)
    strand.add_node(test_case_generator)
    strand.add_node(automation_script_generator)
    strand.add_node(deployment_script_generator)
    strand.add_node(code_executor)
    
    # Connect the nodes
    strand.add_edge(requirements_analyzer, user_story_generator)
    strand.add_edge(user_story_generator, code_generator)
    strand.add_edge(code_generator, test_case_generator, edge_type="code")
    strand.add_edge(user_story_generator, test_case_generator, edge_type="user_stories")
    strand.add_edge(code_generator, automation_script_generator, edge_type="code")
    strand.add_edge(user_story_generator, automation_script_generator, edge_type="user_stories")
    strand.add_edge(code_generator, deployment_script_generator)
    strand.add_edge(code_generator, code_executor, edge_type="code")
    strand.add_edge(deployment_script_generator, code_executor, edge_type="deployment_script")
    
    return strand

# Streamlit UI
st.markdown("""
This application implements a full Software Development Life Cycle (SDLC) pipeline using AI agents.
Upload a requirements document, and the system will:
1. Analyze requirements
2. Generate user stories
3. Create application code
4. Generate unit tests
5. Create Selenium automation scripts
6. Generate deployment scripts
7. Execute the application
""")

# File uploader
uploaded_file = st.file_uploader("Upload Requirements Document", type=["txt", "pdf", "docx"])

if uploaded_file is not None:
    # Read the file
    if uploaded_file.type == "application/pdf":
        st.error("PDF parsing requires additional libraries. Please upload a text file.")
        requirements_doc = ""
    elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        st.error("DOCX parsing requires additional libraries. Please upload a text file.")
        requirements_doc = ""
    else:
        requirements_doc = uploaded_file.getvalue().decode("utf-8")
    
    # Display the requirements
    with st.expander("Requirements Document"):
        st.write(requirements_doc)
    
    # Process button
    if st.button("Start SDLC Pipeline"):
        if requirements_doc:
            # Create and run the pipeline
            pipeline = create_sdlc_pipeline()
            
            # Progress tracking
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            # Create tabs for outputs
            tabs = st.tabs(["Requirements Analysis", "User Stories", "Code", "Unit Tests", 
                           "Automation Scripts", "Deployment Scripts", "Execution"])
            
            # Run the pipeline
            try:
                # Initialize the pipeline with the requirements document
                pipeline.start(requirements_analyzer=requirements_doc)
                
                # Update progress
                for i, tab_name in enumerate(["Requirements Analysis", "User Stories", "Code", "Unit Tests", 
                                             "Automation Scripts", "Deployment Scripts", "Execution"]):
                    status_text.text(f"Processing: {tab_name}")
                    progress_bar.progress((i+1)/7)
                
                # Display results in tabs
                with tabs[0]:
                    st.markdown("### Requirements Analysis")
                    st.code(pipeline.get_node_output(RequirementsAnalyzer))
                
                with tabs[1]:
                    st.markdown("### User Stories")
                    st.code(pipeline.get_node_output(UserStoryGenerator))
                
                with tabs[2]:
                    st.markdown("### Generated Code")
                    st.code(pipeline.get_node_output(CodeGenerator))
                
                with tabs[3]:
                    st.markdown("### Unit Tests")
                    st.code(pipeline.get_node_output(TestCaseGenerator))
                
                with tabs[4]:
                    st.markdown("### Automation Scripts")
                    st.code(pipeline.get_node_output(AutomationScriptGenerator))
                
                with tabs[5]:
                    st.markdown("### Deployment Scripts")
                    st.code(pipeline.get_node_output(DeploymentScriptGenerator))
                
                with tabs[6]:
                    st.markdown("### Execution Results")
                    st.code(pipeline.get_node_output(CodeExecutor))
                
                status_text.text("SDLC Pipeline completed successfully!")
                
            except Exception as e:
                st.error(f"Error in SDLC pipeline: {str(e)}")
        else:
            st.error("Please upload a valid requirements document.")

# Add information about required dependencies
with st.expander("Setup Instructions"):
    st.markdown("""
    ### Required Dependencies
    
    To run this application, you need:
    
    ```bash
    pip install streamlit strands-agents ollama
    ```
    
    You also need to have Ollama installed and running with the following models:
    - llama3
    - codellama
    
    Install Ollama from: https://ollama.ai/
    
    Then pull the required models:
    ```bash
    ollama pull llama3
    ollama pull codellama
    ```
    
    Run the application with:
    ```bash
    streamlit run sdlc_pipeline_app.py
    ```
    """)
