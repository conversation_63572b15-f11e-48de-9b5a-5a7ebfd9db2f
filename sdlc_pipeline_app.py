import streamlit as st
import os
import subprocess
from pathlib import Path
from typing import Dict
import zipfile
import io

# Import Strands Agents
from strands import Agent
from strands.models.ollama import OllamaModel

# Configure the page
st.set_page_config(page_title="SDLC Pipeline with AI Agents", layout="wide")
st.title("🚀 SDLC Pipeline with AI Agents")

# Initialize session state
if 'workflow_results' not in st.session_state:
    st.session_state.workflow_results = {}
if 'current_workflow_id' not in st.session_state:
    st.session_state.current_workflow_id = None

# Create project directory
PROJECT_DIR = Path("generated_projects")
PROJECT_DIR.mkdir(exist_ok=True)

# Helper functions for file operations
def create_project_structure(project_name: str) -> Path:
    """Create a project directory structure"""
    project_path = PROJECT_DIR / project_name
    project_path.mkdir(exist_ok=True)

    # Create subdirectories
    (project_path / "src").mkdir(exist_ok=True)
    (project_path / "tests").mkdir(exist_ok=True)
    (project_path / "automation").mkdir(exist_ok=True)
    (project_path / "deployment").mkdir(exist_ok=True)

    return project_path

def save_file_content(file_path: Path, content: str):
    """Save content to a file"""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def create_zip_download(project_path: Path) -> bytes:
    """Create a zip file of the project for download"""
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for file_path in project_path.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(project_path)
                zip_file.write(file_path, arcname)
    return zip_buffer.getvalue()

def extract_agent_response(response) -> str:
    """Extract text content from agent response"""
    if hasattr(response, 'message'):
        if isinstance(response.message, dict):
            # Handle Strands Agent response format
            content = response.message.get('content', [])
            if isinstance(content, list) and len(content) > 0:
                return content[0].get('text', str(response.message))
            else:
                return str(response.message)
        else:
            return str(response.message)
    else:
        return str(response)

# Create specialized agents using proper Strands API
def create_sdlc_agents():
    """Create all SDLC agents with Ollama models"""

    # Use Ollama models for local execution
    llama_model = OllamaModel(
        host="http://localhost:11434",
        model_id="llama3"
    )
    codellama_model = OllamaModel(
        host="http://localhost:11434",
        model_id="codellama"
    )

    # Requirements Analyzer Agent
    requirements_agent = Agent(
        model=llama_model,
        system_prompt="""You are an expert requirements analyst. Your job is to:
        1. Analyze requirements documents thoroughly
        2. Extract key functional and non-functional requirements
        3. Identify stakeholders and their needs
        4. Structure requirements in a clear, organized format
        5. Identify potential risks and constraints

        Provide your analysis in a structured format with clear sections.""",
        callback_handler=None
    )

    # User Story Generator Agent
    user_story_agent = Agent(
        model=llama_model,
        system_prompt="""You are a product owner expert at creating user stories. Your job is to:
        1. Convert requirements into well-formed user stories
        2. Follow the format: "As a [user type], I want [functionality] so that [benefit]"
        3. Include acceptance criteria for each story
        4. Prioritize stories by business value
        5. Ensure stories are testable and implementable

        Create comprehensive user stories with clear acceptance criteria.""",
        callback_handler=None
    )

    # Code Generator Agent
    code_agent = Agent(
        model=codellama_model,
        system_prompt="""You are a senior software developer. Your job is to:
        1. Generate clean, well-structured code based on user stories
        2. Follow best practices and design patterns
        3. Include proper error handling and validation
        4. Write modular, maintainable code
        5. Include necessary imports and dependencies
        6. Create a complete, runnable application

        Generate production-ready code with proper structure and documentation.""",
        callback_handler=None
    )

    # Test Case Generator Agent
    test_agent = Agent(
        model=codellama_model,
        system_prompt="""You are a QA engineer specializing in unit testing. Your job is to:
        1. Create comprehensive unit tests for the provided code
        2. Test both positive and negative scenarios
        3. Include edge cases and boundary conditions
        4. Use appropriate testing frameworks (pytest, unittest, etc.)
        5. Ensure good test coverage
        6. Write clear, maintainable test code

        Generate complete test suites that thoroughly validate the application.""",
        callback_handler=None
    )

    # Automation Script Generator Agent
    automation_agent = Agent(
        model=codellama_model,
        system_prompt="""You are a QA automation engineer specializing in Selenium. Your job is to:
        1. Create Selenium automation scripts for web applications
        2. Test user workflows and critical paths
        3. Include proper waits and error handling
        4. Use page object model when appropriate
        5. Create maintainable and reliable tests
        6. Include setup and teardown procedures

        Generate robust Selenium automation scripts that validate user stories.""",
        callback_handler=None
    )

    # Deployment Script Generator Agent
    deployment_agent = Agent(
        model=codellama_model,
        system_prompt="""You are a DevOps engineer specializing in deployment automation. Your job is to:
        1. Create deployment scripts (Docker, docker-compose, etc.)
        2. Include environment configuration
        3. Set up proper dependency management
        4. Include health checks and monitoring
        5. Follow security best practices
        6. Create production-ready deployment configurations

        Generate complete deployment solutions that are secure and scalable.""",
        callback_handler=None
    )

    return {
        'requirements': requirements_agent,
        'user_stories': user_story_agent,
        'code': code_agent,
        'tests': test_agent,
        'automation': automation_agent,
        'deployment': deployment_agent
    }

# SDLC Pipeline Execution Functions
def execute_sdlc_pipeline(requirements_doc: str, project_name: str) -> Dict[str, str]:
    """Execute the complete SDLC pipeline using Strands agents"""

    # Create agents
    agents = create_sdlc_agents()

    # Create project structure
    project_path = create_project_structure(project_name)

    # Initialize results dictionary
    results = {}

    # Step 1: Requirements Analysis
    st.write("📝 Analyzing requirements document...")
    requirements_analysis = agents['requirements'](
        f"Analyze this requirements document and provide a structured summary:\n\n{requirements_doc}"
    )
    results['requirements_analysis'] = extract_agent_response(requirements_analysis)

    # Step 2: Generate User Stories
    st.write("📚 Generating user stories...")
    user_stories = agents['user_stories'](
        f"Create comprehensive user stories from these requirements:\n\n{results['requirements_analysis']}"
    )
    results['user_stories'] = extract_agent_response(user_stories)

    # Step 3: Generate Code
    st.write("💻 Generating application code...")
    code_prompt = f"""Generate a complete, production-ready application based on these user stories:

{results['user_stories']}

Requirements:
1. Create a well-structured application with proper file organization
2. Include all necessary imports and dependencies
3. Follow best practices and design patterns
4. Include error handling and validation
5. Make the code modular and maintainable
6. Include a main entry point and proper configuration

Provide the complete application code with clear file structure."""

    generated_code = agents['code'](code_prompt)
    results['code'] = extract_agent_response(generated_code)

    # Save the main application code
    save_file_content(project_path / "src" / "app.py", results['code'])

    # Step 4: Generate Unit Tests
    st.write("🧪 Generating unit tests...")
    test_prompt = f"""Create comprehensive unit tests for this code:

CODE:
{results['code']}

USER STORIES:
{results['user_stories']}

Requirements:
1. Use pytest framework
2. Test all major functions and classes
3. Include positive and negative test cases
4. Test edge cases and boundary conditions
5. Ensure good test coverage
6. Include setup and teardown methods where needed

Provide complete test files with proper structure."""

    unit_tests = agents['tests'](test_prompt)
    results['unit_tests'] = extract_agent_response(unit_tests)

    # Save unit tests
    save_file_content(project_path / "tests" / "test_app.py", results['unit_tests'])

    return results, project_path

def continue_pipeline_execution(results: Dict[str, str], project_path: Path) -> Dict[str, str]:
    """Continue with automation scripts, deployment, and execution"""

    # Create agents
    agents = create_sdlc_agents()

    # Step 5: Generate Selenium Automation Scripts
    st.write("🤖 Generating Selenium automation scripts...")
    automation_prompt = f"""Create Selenium automation scripts for this application:

CODE:
{results['code']}

USER STORIES:
{results['user_stories']}

Requirements:
1. Use Selenium WebDriver with Python
2. Test the main user workflows
3. Include proper waits and error handling
4. Use page object model pattern
5. Include setup and teardown methods
6. Test critical user paths from the user stories

Provide complete Selenium test scripts."""

    automation_scripts = agents['automation'](automation_prompt)
    results['automation_scripts'] = extract_agent_response(automation_scripts)

    # Save automation scripts
    save_file_content(project_path / "automation" / "test_automation.py", results['automation_scripts'])

    # Step 6: Generate Deployment Scripts
    st.write("🚀 Generating deployment scripts...")
    deployment_prompt = f"""Create deployment scripts for this application:

CODE:
{results['code']}

Requirements:
1. Create a Dockerfile for containerization
2. Create docker-compose.yml for orchestration
3. Include environment configuration
4. Add health checks and monitoring
5. Include requirements.txt with dependencies
6. Follow security best practices
7. Make it production-ready

Provide complete deployment configuration."""

    deployment_scripts = agents['deployment'](deployment_prompt)
    results['deployment_scripts'] = extract_agent_response(deployment_scripts)

    # Save deployment files
    save_file_content(project_path / "deployment" / "Dockerfile", results['deployment_scripts'])

    # Step 7: Execute the Application
    st.write("▶️ Executing the application...")
    execution_result = execute_generated_application(project_path, results)
    results['execution_result'] = execution_result

    return results

def execute_generated_application(project_path: Path, results: Dict[str, str]) -> str:
    """Execute the generated application safely"""
    try:
        # Create requirements.txt
        requirements_content = """streamlit
flask
fastapi
requests
pytest
selenium
webdriver-manager
python-dotenv"""

        save_file_content(project_path / "requirements.txt", requirements_content)

        # Create a simple execution script
        execution_script = f"""#!/bin/bash
# SDLC Pipeline Execution Script
echo "Setting up virtual environment..."
python -m venv venv
source venv/bin/activate

echo "Installing dependencies..."
pip install -r requirements.txt

echo "Running unit tests..."
python -m pytest tests/ -v

echo "Application setup completed!"
echo "Project structure:"
find . -type f -name "*.py" | head -10
"""

        save_file_content(project_path / "run.sh", execution_script)
        os.chmod(project_path / "run.sh", 0o755)

        # Run basic validation
        result = subprocess.run(
            ["python", "-m", "py_compile", str(project_path / "src" / "app.py")],
            capture_output=True,
            text=True,
            cwd=project_path
        )

        if result.returncode == 0:
            return f"✅ Application generated successfully!\n\nProject created at: {project_path}\n\nFiles created:\n- src/app.py (Main application)\n- tests/test_app.py (Unit tests)\n- automation/test_automation.py (Selenium tests)\n- deployment/Dockerfile (Deployment config)\n- requirements.txt (Dependencies)\n- run.sh (Execution script)\n\nThe application code compiles successfully!"
        else:
            return f"⚠️ Application generated with syntax issues:\n{result.stderr}\n\nProject created at: {project_path}\nPlease review and fix the generated code."

    except Exception as e:
        return f"❌ Error during execution: {str(e)}\n\nProject created at: {project_path}"

# Streamlit UI
st.markdown("""
## 🎯 Full SDLC Pipeline with AI Agents

This application implements a complete Software Development Life Cycle (SDLC) pipeline using AI agents that collaborate to:

1. **📝 Analyze Requirements** - Extract and structure requirements
2. **📚 Generate User Stories** - Create detailed user stories with acceptance criteria
3. **💻 Generate Code** - Create production-ready application code
4. **🧪 Create Unit Tests** - Generate comprehensive test suites
5. **🤖 Build Automation Scripts** - Create Selenium automation tests
6. **🚀 Generate Deployment Scripts** - Create Docker and deployment configurations
7. **▶️ Execute Application** - Validate and run the generated application

### 🚀 Get Started
Upload a requirements document below to begin the automated SDLC process!
""")

# Input section
col1, col2 = st.columns([2, 1])

with col1:
    # File uploader
    uploaded_file = st.file_uploader("Upload Requirements Document", type=["txt", "pdf", "docx"])

with col2:
    # Project name input
    project_name = st.text_input("Project Name", value="my_project", help="Name for the generated project")

# Sample requirements option
if st.checkbox("Use Sample Requirements Document"):
    sample_requirements = """
# E-Commerce Web Application Requirements

## Project Overview
Develop a modern e-commerce web application that allows users to browse products, add items to cart, and complete purchases.

## Functional Requirements

### User Management
- User registration and login
- User profile management
- Password reset functionality

### Product Catalog
- Display products with images, descriptions, and prices
- Product search and filtering
- Product categories and subcategories
- Product reviews and ratings

### Shopping Cart
- Add/remove items from cart
- Update item quantities
- Calculate total price including taxes
- Save cart for logged-in users

### Checkout Process
- Secure payment processing
- Order confirmation
- Email notifications
- Order history for users

### Admin Panel
- Product management (CRUD operations)
- Order management
- User management
- Sales analytics

## Non-Functional Requirements
- Responsive design for mobile and desktop
- Fast loading times (< 3 seconds)
- Secure payment processing
- SEO-friendly URLs
- Accessibility compliance

## Technical Requirements
- Web-based application
- Modern UI/UX design
- Database for data persistence
- RESTful API architecture
"""
    requirements_doc = sample_requirements
    uploaded_file = None
else:
    if uploaded_file is not None:
        # Read the file
        if uploaded_file.type == "application/pdf":
            st.error("PDF parsing requires additional libraries. Please upload a text file.")
            requirements_doc = ""
        elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            st.error("DOCX parsing requires additional libraries. Please upload a text file.")
            requirements_doc = ""
        else:
            requirements_doc = uploaded_file.getvalue().decode("utf-8")
    else:
        requirements_doc = ""

# Display the requirements
if requirements_doc:
    with st.expander("📄 Requirements Document Preview", expanded=False):
        st.markdown(requirements_doc)

# Process button
if st.button("🚀 Start SDLC Pipeline", type="primary", use_container_width=True):
    if requirements_doc and project_name:
        # Create a unique project name with timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_project_name = f"{project_name}_{timestamp}"

        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()

        # Create tabs for outputs
        tabs = st.tabs(["📝 Requirements", "📚 User Stories", "💻 Code", "🧪 Tests",
                       "🤖 Automation", "🚀 Deployment", "▶️ Execution"])

        # Run the pipeline
        try:
            status_text.text("🚀 Starting SDLC Pipeline...")
            progress_bar.progress(0.1)

            # Execute the first part of the pipeline
            results, project_path = execute_sdlc_pipeline(requirements_doc, unique_project_name)
            progress_bar.progress(0.6)

            # Continue with automation, deployment, and execution
            status_text.text("🔄 Continuing with automation and deployment...")
            final_results = continue_pipeline_execution(results, project_path)
            progress_bar.progress(1.0)

            status_text.text("✅ SDLC Pipeline completed successfully!")

            # Display results in tabs
            with tabs[0]:
                st.markdown("### 📝 Requirements Analysis")
                st.markdown(final_results['requirements_analysis'])

            with tabs[1]:
                st.markdown("### 📚 User Stories")
                st.markdown(final_results['user_stories'])

            with tabs[2]:
                st.markdown("### 💻 Generated Code")
                st.code(final_results['code'], language='python')

            with tabs[3]:
                st.markdown("### 🧪 Unit Tests")
                st.code(final_results['unit_tests'], language='python')

            with tabs[4]:
                st.markdown("### 🤖 Automation Scripts")
                st.code(final_results['automation_scripts'], language='python')

            with tabs[5]:
                st.markdown("### 🚀 Deployment Scripts")
                st.code(final_results['deployment_scripts'], language='dockerfile')

            with tabs[6]:
                st.markdown("### ▶️ Execution Results")
                st.success(final_results['execution_result'])

                # Download button for the project
                if project_path.exists():
                    zip_data = create_zip_download(project_path)
                    st.download_button(
                        label="📦 Download Complete Project",
                        data=zip_data,
                        file_name=f"{unique_project_name}.zip",
                        mime="application/zip"
                    )

            # Store results in session state
            st.session_state.workflow_results = final_results
            st.session_state.current_workflow_id = unique_project_name

        except Exception as e:
            st.error(f"❌ Error in SDLC pipeline: {str(e)}")
            st.exception(e)
    else:
        if not requirements_doc:
            st.error("Please upload a requirements document or use the sample requirements.")
        if not project_name:
            st.error("Please provide a project name.")

# Sidebar with information and controls
with st.sidebar:
    st.markdown("## 🛠️ Setup Instructions")

    with st.expander("📋 Prerequisites", expanded=False):
        st.markdown("""
        ### Required Dependencies

        Install the required Python packages:
        ```bash
        pip install streamlit strands-agents strands-agents-tools
        ```

        ### Ollama Setup
        1. Install Ollama from: https://ollama.ai/
        2. Pull the required models:
        ```bash
        ollama pull llama3
        ollama pull codellama
        ```
        3. Ensure Ollama is running before starting the application
        """)

    with st.expander("🚀 Running the Application", expanded=False):
        st.markdown("""
        ### Start the Application
        ```bash
        streamlit run sdlc_pipeline_app.py
        ```

        ### Features
        - ✅ Requirements analysis
        - ✅ User story generation
        - ✅ Code generation
        - ✅ Unit test creation
        - ✅ Selenium automation scripts
        - ✅ Deployment configurations
        - ✅ Project execution and validation
        - ✅ Complete project download
        """)

    with st.expander("📁 Project Structure", expanded=False):
        st.markdown("""
        ### Generated Project Layout
        ```
        project_name/
        ├── src/
        │   └── app.py          # Main application
        ├── tests/
        │   └── test_app.py     # Unit tests
        ├── automation/
        │   └── test_automation.py  # Selenium tests
        ├── deployment/
        │   └── Dockerfile      # Deployment config
        ├── requirements.txt    # Dependencies
        └── run.sh             # Execution script
        ```
        """)

    # Display current workflow status
    if st.session_state.current_workflow_id:
        st.markdown("## 📊 Current Session")
        st.info(f"**Project:** {st.session_state.current_workflow_id}")

        if st.button("🗑️ Clear Session"):
            st.session_state.workflow_results = {}
            st.session_state.current_workflow_id = None
            st.rerun()

# Footer
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #666;'>
    <p>🤖 Powered by Strands Agents & Ollama | Built with Streamlit</p>
    <p>This application demonstrates a complete AI-driven SDLC pipeline with agent collaboration</p>
</div>
""", unsafe_allow_html=True)
