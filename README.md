# 🚀 SDLC Pipeline with AI Agents

A comprehensive Software Development Life Cycle (SDLC) pipeline powered by AI agents using Strands Agents and Ollama. This application automates the entire software development process from requirements analysis to deployment and execution.

## 🎯 Features

### Complete SDLC Automation
- **📝 Requirements Analysis** - Intelligent analysis and structuring of requirements documents
- **📚 User Story Generation** - Automatic creation of detailed user stories with acceptance criteria
- **💻 Code Generation** - Production-ready application code generation
- **🧪 Unit Test Creation** - Comprehensive test suite generation with pytest
- **🤖 Automation Scripts** - Selenium automation test scripts for end-to-end testing
- **🚀 Deployment Scripts** - Docker and deployment configuration generation
- **▶️ Application Execution** - Validation and execution of generated applications

### AI Agent Collaboration
- **Specialized Agents** - Each agent is optimized for specific SDLC tasks
- **Context Passing** - Seamless information flow between agents
- **Intelligent Prompting** - Advanced prompts for high-quality outputs
- **Error Handling** - Robust error handling and recovery mechanisms

### User Experience
- **Interactive UI** - Modern Streamlit interface with real-time progress tracking
- **Sample Requirements** - Built-in sample requirements for quick testing
- **Project Download** - Complete project download as ZIP file
- **Real-time Streaming** - Live updates during agent execution

## 🛠️ Prerequisites

### Required Software
1. **Python 3.10+** - Required for Strands Agents
2. **Ollama** - Local LLM runtime
3. **Git** - For version control

### Installation Steps

#### 1. Install Ollama
```bash
# Visit https://ollama.ai/ and follow installation instructions
# Or use the following for Linux/macOS:
curl -fsSL https://ollama.ai/install.sh | sh
```

#### 2. Pull Required Models
```bash
ollama pull llama3
ollama pull codellama
```

#### 3. Install Python Dependencies
```bash
pip install -r requirements.txt
```

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd strands2
pip install -r requirements.txt
```

### 2. Start Ollama (if not running)
```bash
ollama serve
```

### 3. Run the Application
```bash
streamlit run sdlc_pipeline_app.py
```

### 4. Access the Application
Open your browser and navigate to `http://localhost:8501`

## 📋 Usage Guide

### Using Sample Requirements
1. Check "Use Sample Requirements Document" for a pre-built e-commerce example
2. Enter a project name
3. Click "🚀 Start SDLC Pipeline"

### Using Custom Requirements
1. Upload a `.txt` file with your requirements
2. Enter a project name
3. Click "🚀 Start SDLC Pipeline"

### Pipeline Execution
The pipeline will execute the following steps automatically:
1. **Requirements Analysis** - Extracts and structures requirements
2. **User Story Generation** - Creates detailed user stories
3. **Code Generation** - Generates complete application code
4. **Unit Test Creation** - Creates comprehensive test suites
5. **Automation Scripts** - Generates Selenium test scripts
6. **Deployment Scripts** - Creates Docker and deployment configs
7. **Application Execution** - Validates and executes the generated code

### Project Output
Generated projects include:
```
project_name_timestamp/
├── src/
│   └── app.py              # Main application code
├── tests/
│   └── test_app.py         # Unit tests
├── automation/
│   └── test_automation.py  # Selenium automation tests
├── deployment/
│   └── Dockerfile          # Deployment configuration
├── requirements.txt        # Python dependencies
└── run.sh                 # Execution script
```

## 🏗️ Architecture

### Agent Specialization
- **Requirements Agent** - Analyzes and structures requirements
- **User Story Agent** - Creates user stories with acceptance criteria
- **Code Agent** - Generates production-ready application code
- **Test Agent** - Creates comprehensive unit test suites
- **Automation Agent** - Generates Selenium automation scripts
- **Deployment Agent** - Creates deployment configurations

### Technology Stack
- **Frontend**: Streamlit for interactive web interface
- **AI Framework**: Strands Agents for agent orchestration
- **LLM Runtime**: Ollama for local model execution
- **Models**: Llama3 for analysis, CodeLlama for code generation
- **File Management**: Python pathlib for project structure

## 🔧 Configuration

### Model Configuration
The application uses:
- **llama3** - For requirements analysis and user story generation
- **codellama** - For code generation, testing, and deployment scripts

### Customization
You can customize agent prompts and behavior by modifying the `create_sdlc_agents()` function in `sdlc_pipeline_app.py`.

## 📁 Project Structure

```
strands2/
├── sdlc_pipeline_app.py    # Main application
├── requirements.txt        # Python dependencies
├── sample_requirements.txt # Sample requirements document
├── README.md              # This file
└── generated_projects/    # Output directory for generated projects
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Strands Agents** - For the powerful agent framework
- **Ollama** - For local LLM execution
- **Streamlit** - For the interactive web interface
- **Anthropic** - For the Claude models via Ollama

## 📞 Support

For issues and questions:
1. Check the troubleshooting section below
2. Open an issue on GitHub
3. Review the Strands Agents documentation

## 🔍 Troubleshooting

### Common Issues

**Port Already in Use**
```bash
streamlit run sdlc_pipeline_app.py --server.port 8502
```

**Ollama Models Not Found**
```bash
ollama pull llama3
ollama pull codellama
```

**Memory Issues**
- Ensure sufficient RAM (8GB+ recommended)
- Close other applications
- Use smaller models if needed

**Generation Quality Issues**
- Review and improve requirements document clarity
- Ensure requirements are specific and detailed
- Check model responses in the application tabs
