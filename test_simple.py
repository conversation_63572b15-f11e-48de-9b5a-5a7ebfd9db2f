#!/usr/bin/env python3
"""
Simple test to verify Strands Agents with Ollama
"""

try:
    from strands import Agent
    from strands.models import OllamaModel
    print("✅ Successfully imported Strands Agents")
except ImportError as e:
    print(f"❌ Failed to import Strands Agents: {e}")
    exit(1)

try:
    # Create a simple Ollama model
    print("🔄 Creating Ollama model...")
    model = OllamaModel(model_id="llama3")
    print("✅ Ollama model created successfully")
    
    # Create a simple agent
    print("🔄 Creating agent...")
    agent = Agent(
        model=model,
        system_prompt="You are a helpful assistant.",
        callback_handler=None
    )
    print("✅ Agent created successfully")
    
    # Test a simple call
    print("🔄 Testing agent call...")
    response = agent("Say hello in one sentence.")
    print(f"✅ Agent responded: {response.message[:100]}...")
    
    print("\n🎉 All tests passed! The SDLC pipeline should work correctly.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
