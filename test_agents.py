#!/usr/bin/env python3
"""
Test script to verify SDLC agents are working correctly
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from sdlc_pipeline_app import create_sdlc_agents
    print("✅ Successfully imported create_sdlc_agents")
except ImportError as e:
    print(f"❌ Failed to import create_sdlc_agents: {e}")
    sys.exit(1)

def test_agent_creation():
    """Test that all agents can be created successfully"""
    print("\n🧪 Testing agent creation...")
    
    try:
        agents = create_sdlc_agents()
        print(f"✅ Successfully created {len(agents)} agents")
        
        expected_agents = ['requirements', 'user_stories', 'code', 'tests', 'automation', 'deployment']
        for agent_name in expected_agents:
            if agent_name in agents:
                print(f"  ✅ {agent_name} agent created")
            else:
                print(f"  ❌ {agent_name} agent missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ Failed to create agents: {e}")
        return False

def test_simple_agent_call():
    """Test a simple agent call"""
    print("\n🧪 Testing simple agent call...")
    
    try:
        agents = create_sdlc_agents()
        
        # Test requirements agent with a simple prompt
        test_prompt = "Analyze this simple requirement: Create a hello world web application."
        
        print("  📝 Testing requirements agent...")
        response = agents['requirements'](test_prompt)
        
        if response and hasattr(response, 'message') and response.message:
            print(f"  ✅ Requirements agent responded successfully")
            print(f"  📄 Response length: {len(response.message)} characters")
            return True
        else:
            print(f"  ❌ Requirements agent returned invalid response")
            return False
            
    except Exception as e:
        print(f"❌ Failed to test agent call: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting SDLC Agent Tests")
    print("=" * 50)
    
    # Test 1: Agent Creation
    if not test_agent_creation():
        print("\n❌ Agent creation test failed")
        return False
    
    # Test 2: Simple Agent Call
    if not test_simple_agent_call():
        print("\n❌ Agent call test failed")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed successfully!")
    print("\n💡 The SDLC pipeline is ready to use!")
    print("   Run: streamlit run sdlc_pipeline_app.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
